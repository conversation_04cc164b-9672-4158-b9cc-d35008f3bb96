# BRAIN API

## Table of Contents
- [BRAIN API](#brain-api)
  - [Table of Contents](#table-of-contents)
  - [Examples](#examples)
    - [Signing In](#signing-in)
    - [Biometrics Sign In](#biometrics-sign-in)
    - [Simulating an Alpha](#simulating-an-alpha)
    - [Waiting for an Alpha Simulation to End and Retrieving Results](#waiting-for-an-alpha-simulation-to-end-and-retrieving-results)
    - [Getting Record Sets of Alpha Simulations](#getting-record-sets-of-alpha-simulations)
  - [API Documentation](#api-documentation)
    - [`/authentication`](#authentication)
    - [`/simulations`](#simulations)
    - [`/alphas`](#alphas)
    - [`/alphas/<alpha_id>/recordsets`](#alphasalpha_idrecordsets)
    - [`/alphas/<alpha_id>/recordsets/<record set name>`](#alphasalpha_idrecordsetsrecord-set-name)
  - [Additional Details](#additional-details)
    - [Record Sets](#record-sets)
    - [`/users/<userid>/activities/diversity`](#usersuseridactivitiesdiversity)
  - [Troubleshooting common API error messages](#troubleshooting-common-api-error-messages)

---

The **BRAIN** platform sports a rich API library you can use to automate requests to perform certain actions, such as testing alpha expressions with a range of values.

This document will detail a few common actions, such as how to sign in and send an alpha for simulation using the API, as well as describe the API behind these actions.
We will use **Python’s Requests library** to illustrate examples, but you can use any language.

---

## Examples

### Signing In
To sign in to the platform, invoke the `/authentication` endpoint using a **POST** request with a basic authorization header.

```python
import requests

# Create a session to persistently store the headers
s = requests.Session()

s.auth = tuple("email","password")

# Send a POST request to the /authentication API
response = s.post('https://api.worldquantbrain.com/authentication')
````

If successful, a **JWT** is returned and cached by the session.

---

### Biometrics Sign In

If biometrics are enabled, the request will return **401 Unauthorized** with a `WWW-Authenticate: persona` header.

```text
WWW-Authenticate: persona
Location: /authentication/persona?inquiry=inq_XXXXXX
```

You must open this URL in a browser:

```python
from urllib.parse import urljoin

if response.status_code == requests.status_codes.codes.unauthorized:
    if response.headers["WWW-Authenticate"] == "persona":
        input("Complete biometrics authentication and press any key: " +
              urljoin(response.url, response.headers["Location"]))
        s.post(urljoin(response.url, response.headers["Location"]))
    else:
        print("incorrect email and password")
```

---

### Simulating an Alpha

Send a JSON object with simulation settings and expression to `/simulations`.

```python
simulation_data = {
    'type': 'REGULAR',
    'settings': {
        'instrumentType': 'EQUITY',
        'region': 'USA',
        'universe': 'TOP3000',
        'delay': 1,
        'decay': 15,
        'neutralization': 'SUBINDUSTRY',
        'truncation': 0.08,
        'maxTrade': 'ON',
        'pasteurization': 'ON',
        'testPeriod': 'P1Y6M',
        'unitHandling': 'VERIFY',
        'nanHandling': 'OFF',
        'language': 'FASTEXPR',
        'visualization': False,
    },
    'regular': 'close'
}
simulation_response = s.post('https://api.worldquantbrain.com/simulations', json=simulation_data)
```

---

### Waiting for an Alpha Simulation to End and Retrieving Results

Check progress via `Location` header and poll until finished.

```python
from time import sleep

simulation_progress_url = simulation_response.headers['Location']
while True:
    simulation_progress = s.get(simulation_progress_url)
    if simulation_progress.headers.get("Retry-After", 0) == 0:
        break
    print("Sleeping for " + simulation_progress.headers["Retry-After"] + " seconds")
    sleep(float(simulation_progress.headers["Retry-After"]))

print("Alpha done simulating, getting alpha details")
alpha_id = simulation_progress.json()["alpha"]
alpha = s.get("https://api.worldquantbrain.com/alphas/" + alpha_id)
```

---

### Getting Record Sets of Alpha Simulations

Retrieve **PnL** data:

```python
from time import sleep

while True:
    pnl = s.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}/recordsets/pnl")
    if pnl.headers.get("Retry-After", 0) == 0:
        break
    print("Sleeping for " + pnl.headers["Retry-After"] + " seconds")
    sleep(float(pnl.headers["Retry-After"]))
print("PnL retrieved")
```

---

## API Documentation

### `/authentication`

* **GET** → retrieves authentication state
* **POST** → authenticate with credentials
* **DELETE** → remove authentication state

Example **POST Response**:

```json
201 Created
{
    "user": {"id": "<string:user id>"},
    "token": {"expiry": 3600},
    "permissions": ["PERMISSION"]
}
```

---

### `/simulations`

* **OPTIONS** → available properties and allowed values
* **POST** → create new simulation
* **GET** → retrieve simulation state

Example **POST Response**:

```json
201 Created
Location: /simulations/<simulation id>
```

---

### `/alphas`

Manage alphas created by simulations.

* **GET** `/alphas/<alpha id>` → retrieves alpha details

---

### `/alphas/<alpha_id>/recordsets`

* **GET** → lists record sets available for an Alpha

Example:

```json
{
  "count": 19,
  "results": [
    {"name": "pnl", "title": "PnL"},
    {"name": "sharpe", "title": "Sharpe"}
  ]
}
```

---

### `/alphas/<alpha_id>/recordsets/<record set name>`

* **GET** → retrieves given record set (e.g., pnl, sharpe, turnover, etc.)

---

## Additional Details

### Record Sets

Responses may include tabular record sets with schema and records:

```json
{
  "schema": {
    "name": "pnl",
    "title": "PnL",
    "properties": [
      {"name": "date", "title": "Date", "type": "date"},
      {"name": "pnl", "title": "PnL", "type": "decimal"}
    ]
  },
  "records": [
    ["2024-01-01", 0.123],
    ["2024-01-02", -0.045]
  ]
}
```

---

### `/users/<userid>/activities/diversity`

* **GET** → provides alpha submission breakdown by region, delay, and data category.

Example:

```json
200 OK
{
  "alphas": [
    {
      "alphaCount": 10,
      "delay": 1,
      "region": "USA",
      "dataCategory": {"name": "Fundamental", "id": "fundamental"}
    }
  ],
  "count": 10
}
```

---

## Troubleshooting common API error messages

* **Invalid data field**
  → A data field was wrongly used in your Alpha expression.

* **Empty output from ace\_lib function**
  → Some inputs may be invalid or removed from the platform.

* **Got invalid input at index 0, must be an event data**
  → Likely passing a constant into a vector operator.

---
