"""
Main application entry point for BrainSpace session management.
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
import threading

from config import config
from session import session_manager_instance, session_service_instance
from telegram_bot_handler import bot_handler
from brain_api_client import BrainAPIClient
from simulation_worker import SimulationWorker
from sqlalchemy import create_engine, text

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('./log/brainspace_session.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class BrainSpaceApp:
    """Main application class."""

    def __init__(self):
        self.running = False
        self.shutdown_event = asyncio.Event()
        self.session_manager = session_manager_instance
        # Use the API client from the session manager
        self.brain_api_client = self.session_manager.api_client
        self.simulation_worker = SimulationWorker(
            self.session_manager, self.brain_api_client)
        self.simulation_worker_thread = None

    async def start(self):
        """Start the application."""
        logger.info("Starting BrainSpace Session Manager...")

        # Validate configuration
        config_errors = config.validate_config()
        if config_errors:
            logger.error("Configuration errors:")
            for error in config_errors:
                logger.error(f"  - {error}")
            sys.exit(1)

        try:
            # Initialize database schema
            self._init_db_schema()

            # Start session monitoring service
            session_service_instance.start()

            # Start Telegram bot
            await bot_handler.start_bot()

            # Start simulation worker in a separate thread
            self.simulation_worker_thread = threading.Thread(
                target=self.simulation_worker.start)
            # Allow main program to exit even if thread is running
            self.simulation_worker_thread.daemon = True
            self.simulation_worker_thread.start()
            logger.info("Simulation worker thread started.")

            self.running = True
            logger.info("BrainSpace Session Manager started successfully")

            # Send startup notification
            await self._send_startup_notification()

            # Keep the application running until shutdown event is set
            await self.shutdown_event.wait()

        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            await self.stop()
            sys.exit(1)

    async def stop(self):
        """Stop the application."""
        if not self.running:
            return

        logger.info("Stopping BrainSpace Session Manager...")
        self.running = False
        self.shutdown_event.set() # Ensure the event is set if stop is called directly

        try:
            # Stop services
            session_service_instance.stop()
            await bot_handler.stop_bot()

            # Stop simulation worker
            if self.simulation_worker_thread and self.simulation_worker_thread.is_alive():
                self.simulation_worker.stop()
                self.simulation_worker_thread.join(
                    timeout=5)  # Wait for thread to finish
                if self.simulation_worker_thread.is_alive():
                    logger.warning(
                        "Simulation worker thread did not terminate gracefully.")
                else:
                    logger.info("Simulation worker thread stopped.")

            logger.info("BrainSpace Session Manager stopped")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

    async def _send_startup_notification(self):
        """Send startup notification."""
        try:
            status = session_manager_instance.get_session_status()

            if status['authenticated']:
                message = (
                    "🚀 *BrainSpace Started*\n\n"
                    "✅ Session is active\n"
                    f"⏰ Expires: {status['expires_at']}\n"
                    f"🕐 Time remaining: {status['time_remaining']}"
                )
            else:
                message = (
                    "🚀 *BrainSpace Started*\n\n"
                    "❌ No active session\n"
                    "Use /login to authenticate"
                )

            await session_service_instance.notify_custom(message)

        except Exception as e:
            logger.error(f"Failed to send startup notification: {e}")

    def _init_db_schema(self):
        """Initializes the database schema if tables do not exist."""
        engine = create_engine(config.get_database_url())
        with engine.connect() as connection:
            # Check if the ENUM type already exists before creating it
            enum_exists = connection.execute(text("""
                SELECT 1 FROM pg_type WHERE typname = 'alpha_status';
            """)).scalar()

            if not enum_exists:
                connection.execute(text("""
                    CREATE TYPE alpha_status AS ENUM ('none', 'simulating', 'finish', 'error');
                """))
                connection.commit()  # Commit the type creation separately
                logger.info("Created ENUM type 'alpha_status'.")
            else:
                logger.info(
                    "ENUM type 'alpha_status' already exists, skipping creation.")

            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS template (
                    id SERIAL PRIMARY KEY,
                    template TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT NOW()
                );
            """))
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS alpha (
                    id SERIAL PRIMARY KEY,
                    alpha TEXT NOT NULL,
                    status alpha_status DEFAULT 'none',
                    simulation_id TEXT,
                    result JSONB,
                    error TEXT,
                    created_at TIMESTAMP DEFAULT NOW(),
                    simulate_at TIMESTAMP,
                    result_at TIMESTAMP
                );
            """))
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS alpha_reference (
                    id SERIAL PRIMARY KEY,
                    alpha_id INT NOT NULL REFERENCES alpha(id),
                    template_id INT NOT NULL REFERENCES template(id)
                );
            """))
            connection.commit()
        logger.info("Database schema initialized.")


# Global app instance
app = BrainSpaceApp()


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}")
    app.shutdown_event.set()


async def main():
    """Main entry point."""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        await app.start()
    except Exception as e:
        logger.error(f"Application error: {e}")
    finally:
        # Ensure stop is called after the shutdown event is set and awaited
        await app.stop()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application terminated by user via KeyboardInterrupt")
        # The signal handler should have already set the event,
        # and app.stop() is called in main()'s finally block.
        # No need for explicit app.stop() here.
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
