"""
Session management package for BrainSpace.

This package contains all session-related functionality including:
- Session management and lifecycle
- Session data models
- Session monitoring and notifications
- Session utilities and debugging tools
"""

from .session_manager import SessionManager, session_manager_instance
from .session_models import SessionData, AuthResponse
from .session_service import SessionService, session_service_instance

__all__ = [
    'SessionManager',
    'session_manager_instance',
    'SessionData',
    'AuthResponse',
    'SessionService',
    'session_service_instance'
]
