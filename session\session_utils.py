"""
Utility functions for session management operations.
"""

import json
import os
from datetime import datetime
from typing import Optional

from config import config
from .session_manager import session_manager
from .session_models import SessionData


def get_session_info() -> dict:
    """Get detailed session information."""
    session = session_manager.get_session()

    if not session:
        return {
            'status': 'No active session',
            'authenticated': False
        }

    return {
        'status': 'Active',
        'authenticated': True,
        'token_preview': session.token[:20] + '...' if len(session.token) > 20 else session.token,
        'created_at': session.created_at.isoformat(),
        'expires_at': session.expiry.isoformat(),
        'is_expired': session.is_expired,
        'minutes_remaining': session.minutes_until_expiry,
        'user_id': session.user_id
    }


def clear_session() -> bool:
    """Clear current session manually."""
    try:
        session_manager.logout()
        print("✅ Session cleared successfully")
        return True
    except Exception as e:
        print(f"❌ Error clearing session: {e}")
        return False


def export_session_data(filepath: str = "session_export.json") -> bool:
    """Export session data to a file."""
    try:
        info = get_session_info()

        with open(filepath, 'w') as f:
            json.dump(info, f, indent=2)

        print(f"✅ Session data exported to {filepath}")
        return True

    except Exception as e:
        print(f"❌ Error exporting session data: {e}")
        return False


def check_session_file() -> dict:
    """Check the session file on disk."""
    session_file = config.SESSION_FILE_PATH

    if not os.path.exists(session_file):
        return {
            'exists': False,
            'message': f'Session file {session_file} does not exist'
        }

    try:
        with open(session_file, 'r') as f:
            data = json.load(f)

        # Parse expiry to check if expired
        expiry = datetime.fromisoformat(data['expiry'])
        is_expired = datetime.now() >= expiry

        return {
            'exists': True,
            'file_path': session_file,
            'created_at': data.get('created_at'),
            'expires_at': data['expiry'],
            'is_expired': is_expired,
            'user_id': data.get('user_id'),
            'file_size': os.path.getsize(session_file)
        }

    except Exception as e:
        return {
            'exists': True,
            'error': f'Error reading session file: {e}'
        }


def validate_environment() -> dict:
    """Validate environment configuration."""
    validation = {
        'telegram': {
            'bot_token_set': bool(config.TG_BOT_TOKEN and config.TG_BOT_TOKEN != "YOUR_TG_BOT_TOKEN"),
            'chat_id_set': bool(config.TG_CHAT_ID and config.TG_CHAT_ID != "YOUR_TG_CHAT_ID"),
        },
        'brain_api': {
            'user_set': bool(config.BRAIN_USER and config.BRAIN_USER != "your_email"),
            'password_set': bool(config.BRAIN_PASSWORD and config.BRAIN_PASSWORD != "YOUR_PASSWORD"),
            'base_url': config.BRAIN_API_BASE_URL
        },
        'session': {
            'file_path': config.SESSION_FILE_PATH,
            'expiry_hours': config.SESSION_EXPIRY_HOURS,
            'notify_minutes': config.NOTIFY_BEFORE_EXPIRY_MINUTES
        }
    }

    # Overall status
    validation['ready'] = (
        validation['telegram']['bot_token_set'] and
        validation['telegram']['chat_id_set'] and
        validation['brain_api']['user_set'] and
        validation['brain_api']['password_set']
    )

    return validation


def print_status_report():
    """Print a comprehensive status report."""
    print("🔍 BrainSpace Session Status Report")
    print("=" * 50)

    # Environment validation
    print("\n📋 Environment Configuration:")
    env_status = validate_environment()

    print(
        f"   Telegram Bot Token: {'✅' if env_status['telegram']['bot_token_set'] else '❌'}")
    print(
        f"   Telegram Chat ID: {'✅' if env_status['telegram']['chat_id_set'] else '❌'}")
    print(
        f"   Brain API User: {'✅' if env_status['brain_api']['user_set'] else '❌'}")
    print(
        f"   Brain API Password: {'✅' if env_status['brain_api']['password_set'] else '❌'}")
    print(f"   Overall Ready: {'✅' if env_status['ready'] else '❌'}")

    # Session file status
    print("\n💾 Session File Status:")
    file_status = check_session_file()

    if file_status['exists']:
        if 'error' in file_status:
            print(f"   ❌ {file_status['error']}")
        else:
            print(f"   ✅ File exists: {file_status['file_path']}")
            print(f"   📅 Expires: {file_status['expires_at']}")
            print(
                f"   ⏰ Expired: {'Yes' if file_status['is_expired'] else 'No'}")
            print(f"   📊 Size: {file_status['file_size']} bytes")
    else:
        print(f"   ❌ {file_status['message']}")

    # Current session status
    print("\n🔐 Current Session Status:")
    session_info = get_session_info()

    if session_info['authenticated']:
        print(f"   ✅ Status: {session_info['status']}")
        print(f"   🕐 Minutes remaining: {session_info['minutes_remaining']}")
        print(f"   📅 Expires: {session_info['expires_at']}")
    else:
        print(f"   ❌ Status: {session_info['status']}")

    print("\n" + "=" * 50)


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "status":
            print_status_report()
        elif command == "clear":
            clear_session()
        elif command == "export":
            filepath = sys.argv[2] if len(
                sys.argv) > 2 else "session_export.json"
            export_session_data(filepath)
        elif command == "info":
            info = get_session_info()
            print(json.dumps(info, indent=2))
        else:
            print("Available commands:")
            print("  python session_utils.py status  - Show status report")
            print("  python session_utils.py clear   - Clear current session")
            print("  python session_utils.py export  - Export session data")
            print("  python session_utils.py info    - Show session info as JSON")
    else:
        print_status_report()
