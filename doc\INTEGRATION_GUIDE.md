# How to Use Session Management for API Requests

This guide shows you how to use the BrainSpace session management system to make authenticated requests to the Brain API.

## 🚀 Quick Start

### 1. Basic Usage Pattern

```python
from session import session_manager
from brain_api_client import BrainAPIClient

# Get current session
session = session_manager.get_session()
if not session:
    print("No active session. Please login first.")
    return

# Create API client and make request
client = BrainAPIClient()
response = client.make_authenticated_request(
    'GET', '/alphas/your_alpha_id', session.token
)

if response.status_code == 200:
    data = response.json()
    print("Success:", data)
```

### 2. Using the High-Level Service

```python
from brain_api_examples import BrainAPIService

service = BrainAPIService()

# Submit a simulation
simulation_id = service.submit_simulation("close")

# Wait for completion
alpha_id = service.wait_for_simulation(simulation_id)

# Get results
alpha_details = service.get_alpha_details(alpha_id)
pnl_data = service.get_alpha_pnl(alpha_id)
```

## 📋 Available Methods

### Session Manager Methods

```python
from session import session_manager

# Check if authenticated
if session_manager.is_authenticated():
    print("Ready to make requests")

# Get session details
session = session_manager.get_session()
if session:
    print(f"Token expires: {session.expiry}")
    print(f"Minutes left: {session.minutes_until_expiry}")

# Get session status
status = session_manager.get_session_status()
print(f"Authenticated: {status['authenticated']}")
print(f"Time remaining: {status['time_remaining']}")
```

### Brain API Client Methods

```python
from brain_api_client import BrainAPIClient

client = BrainAPIClient()
token = session_manager.get_session().token

# Make any HTTP request
response = client.make_authenticated_request(
    method='POST',           # GET, POST, PUT, DELETE
    endpoint='/simulations', # API endpoint
    token=token,            # Session token
    json={...},             # Request body (for POST/PUT)
    params={...}            # Query parameters
)

# Get authenticated requests session
session = client.get_authenticated_session(token)
response = session.get('https://api.worldquantbrain.com/alphas')
```

## 🎯 Common Use Cases

### 1. Submit Alpha Simulation

```python
from brain_api_examples import BrainAPIService

service = BrainAPIService()

# Basic simulation
simulation_id = service.submit_simulation("close")

# With custom settings
custom_settings = {
    'region': 'EUR',
    'universe': 'TOP1000',
    'delay': 2,
    'testPeriod': 'P2Y'
}
simulation_id = service.submit_simulation("rank(close)", custom_settings)
```

### 2. Monitor Simulation Progress

```python
# Check status manually
status = service.get_simulation_status(simulation_id)
print(f"Status: {status}")

# Wait for completion (blocking)
alpha_id = service.wait_for_simulation(simulation_id, max_wait_minutes=30)
```

### 3. Get Alpha Results

```python
# Get alpha performance metrics
alpha_details = service.get_alpha_details(alpha_id)
print(f"Sharpe: {alpha_details.get('sharpe')}")
print(f"Returns: {alpha_details.get('returns')}")

# Get PnL data
pnl_data = service.get_alpha_pnl(alpha_id)
if pnl_data and 'records' in pnl_data:
    for date, pnl in pnl_data['records']:
        print(f"{date}: {pnl}")

# Get all available recordsets
recordsets = service.get_alpha_recordsets(alpha_id)
for rs in recordsets['results']:
    print(f"Available: {rs['name']} - {rs['title']}")

# Get specific recordset
sharpe_data = service.get_recordset_data(alpha_id, 'sharpe')
turnover_data = service.get_recordset_data(alpha_id, 'turnover')
```

### 4. Handle Session Expiry

```python
def make_safe_request():
    """Example of handling session expiry gracefully."""

    # Check session before making request
    if not session_manager.is_authenticated():
        print("❌ Session expired. Please run /login in Telegram bot.")
        return None

    session = session_manager.get_session()

    # Warn if session expires soon
    if session.minutes_until_expiry < 10:
        print(f"⚠️ Session expires in {session.minutes_until_expiry} minutes")

    # Make request
    service = BrainAPIService()
    return service.get_alpha_details("your_alpha_id")
```

## 🔧 Integration in Your Code

### Option 1: Direct Integration

```python
# In your existing code
from session import session_manager
from brain_api_client import BrainAPIClient

def your_function():
    # Check session
    session = session_manager.get_session()
    if not session:
        raise Exception("No active session")

    # Make request
    client = BrainAPIClient()
    response = client.make_authenticated_request(
        'GET', '/your-endpoint', session.token
    )

    return response.json()
```

### Option 2: Using the Service Class

```python
# In your existing code
from brain_api_examples import BrainAPIService

def your_function():
    service = BrainAPIService()

    # Service handles session management automatically
    result = service.get_alpha_details("alpha_123")

    if result:
        return result
    else:
        raise Exception("Request failed or no session")
```

### Option 3: Custom Service Class

```python
from session import session_manager
from brain_api_client import BrainAPIClient

class MyBrainService:
    def __init__(self):
        self.client = BrainAPIClient()

    def _get_token(self):
        session = session_manager.get_session()
        return session.token if session else None

    def my_custom_method(self):
        token = self._get_token()
        if not token:
            return None

        response = self.client.make_authenticated_request(
            'GET', '/my-custom-endpoint', token
        )

        return response.json() if response.status_code == 200 else None
```

## 🚨 Error Handling

```python
from session import session_manager
from brain_api_examples import BrainAPIService

def robust_api_call():
    try:
        # Check session first
        if not session_manager.is_authenticated():
            print("Please authenticate first: /login")
            return None

        service = BrainAPIService()
        result = service.get_alpha_details("alpha_123")

        if result:
            return result
        else:
            print("API request failed")
            return None

    except Exception as e:
        print(f"Error: {e}")
        return None
```

## 📝 Running Examples

```bash
# Run the example script
python brain_api_examples.py

# Or import and use in your code
from brain_api_examples import example_simple_simulation
example_simple_simulation()
```

## 🔄 Session Lifecycle

1. **Start Session**: Run `/login` in Telegram bot
2. **Use Session**: Make API requests using the methods above
3. **Monitor Expiry**: System sends notifications at 60 and 30 minutes
4. **Refresh Session**: Run `/login` again when needed
5. **Check Status**: Use `/status` in Telegram or `session_manager.get_session_status()`

The session management system handles all the authentication complexity for you - just focus on making your API requests!
