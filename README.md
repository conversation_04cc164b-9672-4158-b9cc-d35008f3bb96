# BrainSpace Session Management

A simple session management system for Brain API with Telegram bot integration.

## Features

- **Automatic Authentication**: Handles standard login + biometric authentication flow
- **Session Lifecycle Management**: Token storage, expiry handling, and refresh
- **Telegram Bot Integration**: Interactive commands and notifications
- **Expiry Notifications**: Alerts at 60 and 30 minutes before expiry
- **Simple Architecture**: Easy to understand and maintain

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configuration

Create a `.env` file with your credentials:

```env
# Telegram Bot Configuration
tg_bot_token=your_telegram_bot_token
tg_chat_id=your_telegram_chat_id

# Brain API Credentials
BRAIN_USER=<EMAIL>
BRAIN_PASSWORD=your_password

# Optional Configuration
SESSION_EXPIRY_HOURS=4
NOTIFY_BEFORE_EXPIRY_MINUTES=60,30
SESSION_FILE_PATH=session_data.json
```

### 3. Run the Application

```bash
python main.py
```

## Telegram Bot Commands

- `/start` - Welcome message and command list
- `/login` - Start authentication process
- `/status` - Check current session status
- `/help` - Show available commands

## Authentication Flow

1. **Initial Login**: Use `/login` command in Telegram
2. **Biometric Required**: If biometric auth is needed:
   - Bot sends you the biometric link
   - Open link in browser and complete verification
   - Click "Complete Biometric Auth" button in Telegram
3. **Session Active**: Authentication successful, session stored

## Session Management

- **Automatic Storage**: Sessions saved to `session_data.json`
- **Expiry Monitoring**: Continuous monitoring with notifications
- **Auto-Refresh**: Manual refresh via `/login` command
- **Status Checking**: Use `/status` to see remaining time

## Architecture

```
main.py                 # Application entry point
├── session/            # Session management package
│   ├── session_manager.py  # Core session management
│   ├── session_service.py  # Monitoring and notifications
│   ├── session_models.py   # Data models
│   ├── session_utils.py    # Utility functions
│   ├── debug_session.py    # Debug tools
│   └── fix_session.py      # Session repair tools
├── brain_api_client.py # Brain API authentication
├── telegram_bot_handler.py # Telegram bot commands
└── config.py          # Configuration management
```

## API Integration

The system integrates with Brain API following the documented authentication flow:

1. POST to `/authentication` with basic auth
2. Handle 401 + `WWW-Authenticate: persona` for biometric
3. Complete biometric auth via persona URL
4. Store and manage JWT tokens

## Logging

Logs are written to:
- `./log/brainspace_session.log` - Application logs
- Console output for real-time monitoring

## Error Handling

- **Network Issues**: Automatic retry with exponential backoff
- **Token Expiry**: Graceful handling with user notifications
- **Configuration Errors**: Validation on startup
- **Bot Failures**: Fallback logging when Telegram is unavailable

## Development

### Adding New Features

1. **New Commands**: Add handlers in `telegram_bot_handler.py`
2. **API Endpoints**: Extend `brain_api_client.py`
3. **Notifications**: Modify `session/session_service.py`
4. **Data Models**: Update `session/session_models.py`

### Testing

```bash
# Test authentication
python -c "from session import session_manager; print(session_manager.authenticate())"

# Test bot commands
# Use Telegram bot interface
```

## Troubleshooting

### Common Issues

1. **Bot Not Responding**
   - Check `tg_bot_token` in `.env`
   - Verify bot is started with BotFather
   - Check `tg_chat_id` is correct

2. **Authentication Fails**
   - Verify `BRAIN_USER` and `BRAIN_PASSWORD`
   - Check network connectivity
   - Review logs for detailed errors

3. **Session Not Persisting**
   - Check file permissions for `session_data.json`
   - Verify disk space availability

### Debug Mode

Set logging level to DEBUG in `main.py`:

```python
logging.basicConfig(level=logging.DEBUG, ...)
```

## Security Notes

- Session tokens stored locally in JSON file
- No passwords stored on disk
- Biometric URLs are temporary and expire quickly
- Use environment variables for sensitive configuration

## License

This project is for internal use with Brain API integration.
