"""
Session data models for BrainSpace session management.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional
from pydantic import BaseModel


class SessionData(BaseModel):
    """Model for session data storage."""
    
    token: str
    expiry: datetime
    user_id: Optional[str] = None
    created_at: datetime
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.now() >= self.expiry
    
    @property
    def time_until_expiry(self) -> timedelta:
        """Get time remaining until expiry."""
        return self.expiry - datetime.now()
    
    @property
    def minutes_until_expiry(self) -> int:
        """Get minutes remaining until expiry."""
        delta = self.time_until_expiry
        return max(0, int(delta.total_seconds() / 60))
    
    def should_notify_expiry(self, notify_minutes: list[int]) -> bool:
        """Check if we should send expiry notification."""
        minutes_left = self.minutes_until_expiry
        return minutes_left in notify_minutes and minutes_left > 0


class AuthResponse(BaseModel):
    """Model for authentication response."""
    
    success: bool
    token: Optional[str] = None
    expiry: Optional[datetime] = None
    error: Optional[str] = None
    requires_biometric: bool = False
    biometric_url: Optional[str] = None


class BiometricAuthRequest(BaseModel):
    """Model for biometric authentication request."""
    
    persona_url: str
    inquiry_id: str
    user_chat_id: str
