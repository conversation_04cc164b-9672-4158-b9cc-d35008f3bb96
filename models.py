from datetime import datetime
from typing import Optional
from enum import Enum
from pydantic import BaseModel

class AlphaStatus(str, Enum):
    NONE = "none"
    SIMULATING = "simulating"
    FINISH = "finish"
    ERROR = "error"

class Template(BaseModel):
    id: Optional[int] = None
    template: str
    created_at: datetime = datetime.now()

class Alpha(BaseModel):
    id: Optional[int] = None
    alpha: str
    status: AlphaStatus = AlphaStatus.NONE
    simulation_id: Optional[str] = None
    result: Optional[dict] = None  # JSONB type in PostgreSQL
    error: Optional[str] = None
    created_at: datetime = datetime.now()
    simulate_at: Optional[datetime] = None
    result_at: Optional[datetime] = None

class AlphaReference(BaseModel):
    id: Optional[int] = None
    alpha_id: int
    template_id: int
